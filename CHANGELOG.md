## [0.1.1](https://github.com/ClashStrategic/deckanalyzer/compare/v0.1.0...v0.1.1) (2025-06-03)


### Bug Fixes

* **DeckAnalyzer:** validate Deck instance properties in constructor ([7a1cf5a](https://github.com/ClashStrategic/deckanalyzer/commit/7a1cf5af1160eafd48af221be942fc1d3a895bad))
* **Dependency:** Remove the dependency on Card by using $allCardsInstanceLevel11 instead to get the data for a specific card ([66ae301](https://github.com/ClashStrategic/deckanalyzer/commit/66ae301362d1982215555bbaff25e203ad81be51))
* **Dependency:** Removes dependency on Config class and PAHT_ROOT ([1b08cfb](https://github.com/ClashStrategic/deckanalyzer/commit/1b08cfbb56317cd799da0aa5b727d271c79d3662))
