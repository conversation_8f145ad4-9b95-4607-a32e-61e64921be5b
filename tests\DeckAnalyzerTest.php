<?php

declare(strict_types=1);

namespace tests;

// Cargar el autoload de Composer
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../DeckAnalyzer.php';
require_once __DIR__ . '/../../../App/Models/Deck.php';
require_once __DIR__ . '/../../../App/Models/Card.php';

use tools\deckanalyzer\DeckAnalyzer;
use App\Models\Deck;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Depends;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;

define('PATH_ROOT', realpath(__DIR__ . '/../../../') . '/');

/**
 * Clase de pruebas unitarias para DeckAnalyzer
 * 
 * Esta clase contiene pruebas unitarias para todos los métodos públicos
 * de la clase DeckAnalyzer, verificando su funcionamiento correcto bajo
 * diferentes escenarios.
 */
class DeckAnalyzerTest extends TestCase
{
    private array $mockDecks = [];
    private object $statCards;
    private object $constants;
    private array $pathsStrategies;

    protected function setUp(): void
    {
        // Mock del deck con 9 cartas
        $this->mockDecks = [
            "asserts" => [
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess"], //normal
                ["Wall Breakers", "Mighty Miner", "Goblin Machine", "Magic Archer", "Three Musketeers", "Lumberjack", "Arrows", "Lightning", "Dagger Duchess"], //random control
                ["Skeleton Barrel", "Mighty Miner", "Goblin Demolisher", "Magic Archer", "Spear Goblins", "Lumberjack", "The log", "Fireball", "Tower Princess"], //random ciclo
                ["Goblin Cage", "Goblin Barrel", "Goblinstein", "Goblin Machine", "Witch", "Electro Spirit", "Barbarian Barrel", "Poison", "Tower Princess"] //random push
            ],
            "exceptions" => [
                ["Firecracker", "Ice Spirit", "Hog Rider"], //menos de 9 cartas
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess", "Goblin Barrel"], //más de 9 cartas
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess", "Goblin Barrel", "Goblin Barrel"], //cartas repetidas
                ["", "", "", "", "", "", "", "", ""], //cartas vacías
                [] //sin cartas
            ],
        ];

        $this->statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $this->constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Constants.json'));
        $this->pathsStrategies = [
            "General" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/General.json',
            "Defensa" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Defense.json',
            "Adaptavilidad" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Adaptability.json',
            "Agresivo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Aggressive.json',
            "Bait" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Bait.json',
            "Ciclo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Cycle.json',
            "Precion" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Pressure.json',
            "Push" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Push.json',
            "SplitPush" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/SplitPush.json'
        ];
    }

    #[Test]
    #[TestDox('Prueba los casos válidos del constructor')]
    public function testConstructorValid(): array
    {
        $deckAnalyzers = [];
        foreach ($this->mockDecks['asserts'] as $deckNames) {
            foreach (['basic', 'intermediate', 'advanced'] as $type) {
                $Deck = new Deck($deckNames);
                try {
                    $deckAnalyzer = new DeckAnalyzer(
                        $this->statCards,
                        $this->constants,
                        $this->pathsStrategies,
                        $Deck,
                        $type
                    );
                    $this->assertInstanceOf(DeckAnalyzer::class, $deckAnalyzer);
                    $deckAnalyzers[] = $deckAnalyzer;
                } catch (\Exception $e) {
                    $this->fail("Unexpected exception: " . $e->getMessage());
                }
            }
        }
        return $deckAnalyzers;
    }

    #[Test]
    #[TestDox('Prueba las excepciones del constructor')]
    public function testConstructorInvalid(): void
    {
        foreach ($this->mockDecks['exceptions'] as $deckNames) {
            foreach (['basic', 'intermediate', 'advanced'] as $type) {
                $this->expectException(\Exception::class);
                $Deck = new Deck($deckNames);
                new DeckAnalyzer(
                    $this->statCards,
                    $this->constants,
                    $this->pathsStrategies,
                    $Deck,
                    $type
                );
            }
        }
    }

    #[Test]
    #[TestDox('Prueba los resultados de la funcion getScoreSynergy')]
    #[Depends('testConstructorValid')]
    public function getScoreSynergyTest($deckAnalyzers): void
    {
        foreach ($deckAnalyzers as $deckAnalyzer) {
            $result = $deckAnalyzer->getScoreSynergy(2);
            $this->assertArrayHasKey('totalScore', $result);

            $this->assertGreaterThanOrEqual(0, $result['totalScore']);
            $this->assertLessThanOrEqual(100, $result['totalScore']);
        }
    }
}
