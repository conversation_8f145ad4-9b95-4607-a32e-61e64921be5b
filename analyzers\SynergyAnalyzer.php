<?php

declare(strict_types=1);

namespace tools\deckanalyzer\analyzers;

use tools\deckanalyzer\AnalyzerInterface;

/**
 * Analizador de Sinergias - Evalúa las sinergias entre cartas y con estrategias.
 *
 * Este analizador se encarga de calcular las sinergias del mazo considerando:
 * - Sinergias entre cartas individuales
 * - Sinergias con estrategias generales
 * - Puntuaciones y análisis detallados de interacciones
 */
class SynergyAnalyzer implements AnalyzerInterface
{
    /** @var object El mazo que se está analizando */
    private object $deck;

    /** @var object Constantes utilizadas para los cálculos */
    private object $constants;

    /** @var array Estrategias disponibles para el análisis */
    private array $strategies;

    /** @var array Todas las cartas con estadísticas de nivel 11 */
    private array $allCardsInstanceLevel11;

    public function __construct(object $constants, array $strategies, array $allCardsInstanceLevel11)
    {
        $this->constants = $constants;
        $this->strategies = $strategies;
        $this->allCardsInstanceLevel11 = $allCardsInstanceLevel11;
    }

    public function analyze(object $deck): array
    {
        $this->deck = $deck;
        return $this->getScoreSynergy();
    }

    /**
     * Analiza los promedios de sinergia del mazo, considerando sinergias de cartas y estrategias.
     *
     * @return array Retorna un array con los siguientes elementos:
     *               - 'totalScore': Promedio total de sinergia.
     *               - 'totalPointsSynergyCards': Puntos de sinergia totales entre cartas.
     *               - 'totalPointsSynergyStrategy': Puntos de sinergia totales con la estrategia.
     *               - 'arrayTotalPointsSynergyStrategy': Array con los puntos de sinergia de estrategia por carta.
     *               - 'arrayTotalPointsSynergyCards': Array con los puntos de sinergia entre cartas por carta.
     *               - 'maximumPossiblePointsStrategy': Máximos puntos posibles de sinergia de estrategia.
     *               - 'maximumPossiblePointsCards': Máximos puntos posibles de sinergia entre cartas.
     *               - 'arraySynergyCards': Detalles de sinergia entre cartas.
     *               - 'arraySynergyStrategy': Detalles de sinergia de estrategia.
     *               - 'msg': Mensajes informativos o de advertencia sobre la sinergia.
     *               - 'topPositiveSynergies': Top 3 sinergias más positivas.
     *               - 'topNegativeSynergies': Top 3 sinergias más negativas.
     * @throws \Exception Si ocurre un error al buscar sinergias o decodificar datos.
     */
    public function getScoreSynergy(): array
    {
        $messages = $arrayTotalPointsSynergyStrategy = $arrayTotalPointsSynergyCards =
            $arraySynergyCards = $arraySynergyStrategy = [];
        $totalNumCards = 0;

        // Refactored loop using foreach with index (though index is not strictly used here)
        foreach (array_merge($this->deck->Cards, $this->deck->CardsEvo) as $index => $card) {
            $totalNumCards++;

            $synergyCards = $this->searchSynergiesCard(
                $card,
                'Analisis',
                'Cards'
            );
            // Store synergy details for cards
            $arraySynergyCards[] = array_merge($synergyCards, ['name' => $card->name, 'medium' => $card->urlIcon]);

            $synergyStrategy = $this->searchSynergiesCard(
                $card,
                'Analisis',
                'General'
            );
            // Store synergy details for strategy
            $arraySynergyStrategy[] = array_merge($synergyStrategy, ['name' => $card->name, 'medium' => $card->urlIcon]);

            // Store individual synergy points
            $arrayTotalPointsSynergyCards[] = $synergyCards['pointsSynergy'];
            $arrayTotalPointsSynergyStrategy[] = $synergyStrategy['pointsSynergy'];
        }

        // Calculate maximum possible points after the loop using max(array_column(...))
        // Added checks for empty arrays to prevent errors/warnings
        $maximumPossiblePointsCards = !empty($arraySynergyCards) ? max(array_column($arraySynergyCards, 'maximumPossiblePoints')) : 0;
        $maximumPossiblePointsStrategy = !empty($arraySynergyStrategy) ? max(array_column($arraySynergyStrategy, 'maximumPossiblePoints')) : 0;

        // Continue with existing logic after loop
        count($arrayTotalPointsSynergyStrategy) != $totalNumCards &&
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: No todas las cartas de tu mazo se integran completamente con la estrategia general, "' . ($totalNumCards - count($arrayTotalPointsSynergyStrategy)) . ' cartas" no presentan sinergias en este análisis.</span>';

        count($arrayTotalPointsSynergyCards) != $totalNumCards &&
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: No todas las cartas de tu mazo tienen sinergias directas, "' . ($totalNumCards - count($arrayTotalPointsSynergyCards)) . ' cartas" no presentan sinergias en este análisis.</span>';

        // Avoid division by zero if arrays are empty
        $averageStrategy = !empty($arrayTotalPointsSynergyStrategy) ? round(array_sum($arrayTotalPointsSynergyStrategy) / count($arrayTotalPointsSynergyStrategy)) : 0;
        $averageCards = !empty($arrayTotalPointsSynergyCards) ? round(array_sum($arrayTotalPointsSynergyCards) / count($arrayTotalPointsSynergyCards)) : 0;

        // Avoid division by zero for scores
        $scoreStrategy = ($maximumPossiblePointsStrategy > 0) ? round(($averageStrategy / ($maximumPossiblePointsStrategy * 0.5)) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreCards = ($maximumPossiblePointsCards > 0) ? round(num: ($averageCards / ($maximumPossiblePointsCards * 0.5)) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreStrategyTotalSynergy = ($totalNumCards > 0) ? round(((count($arrayTotalPointsSynergyStrategy) / $totalNumCards) * 0.75) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreCardsTotalSynergy = ($totalNumCards > 0) ? round(((count($arrayTotalPointsSynergyCards) / $totalNumCards) * 0.75) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

        $totalScore = round(($scoreStrategy + $scoreCards + $scoreStrategyTotalSynergy + $scoreCardsTotalSynergy) / $this->constants->SYNERGY_AVERAGE_DIVISOR);

        if ($totalScore < 25) {
            $messages[] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: La sinergia total del mazo es muy baja (' . $totalScore . '). Considera revisar las sinergias entre cartas y con la estrategia general.</span>';
        } elseif ($totalScore < 50) {
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: La sinergia total del mazo es moderada (' . $totalScore . '). Podrías mejorarla revisando las sinergias individuales.</span>';
        }

        // Calculate top positive and negative synergies
        $allSynergies = [];
        // Strategy Synergies
        foreach ($arraySynergyStrategy as $strategySynergy) {
            $allSynergies[] = [
                'context' => $strategySynergy['name'] . ' <-> Strategy',
                'score' => $strategySynergy['pointsSynergy']
            ];
        }
        // Card-to-Card Synergies
        $allDeckCards = array_merge($this->deck->Cards, $this->deck->CardsEvo); // Cache deck cards
        foreach ($arraySynergyCards as $cardSynergy) {
            foreach ($cardSynergy['SynergyCards'] as $cardDeckKey => $interaction) {
                // Ensure the key exists before accessing
                $targetCardName = isset($allDeckCards[$cardDeckKey]) ? $allDeckCards[$cardDeckKey]->name : 'Unknown Card (' . $cardDeckKey . ')';
                $allSynergies[] = [
                    'context' => $cardSynergy['name'] . ' <-> ' . $targetCardName,
                    'score' => $interaction['pointsSynergy']
                ];
            }
        }

        // Sort ascending by score to find lowest (most negative or least positive)
        usort($allSynergies, fn($a, $b) => $a['score'] <=> $b['score']);
        $topNegativeSynergies = array_slice($allSynergies, 0, 3);

        // Sort descending by score to find highest (most positive)
        usort($allSynergies, fn($a, $b) => $b['score'] <=> $a['score']);
        $topPositiveSynergies = array_slice($allSynergies, 0, 3);

        return [
            "totalScore" => $totalScore,
            "totalPointsSynergyCards" => array_sum($arrayTotalPointsSynergyCards),
            "totalPointsSynergyStrategy" => array_sum($arrayTotalPointsSynergyStrategy),
            "arrayTotalPointsSynergyStrategy" => $arrayTotalPointsSynergyStrategy,
            "arrayTotalPointsSynergyCards" => $arrayTotalPointsSynergyCards,
            "maximumPossiblePointsStrategy" => $maximumPossiblePointsStrategy,
            "maximumPossiblePointsCards" => $maximumPossiblePointsCards,
            "arraySynergyCards" => $arraySynergyCards,
            "arraySynergyStrategy" => $arraySynergyStrategy,
            'msg' => $messages,
            'topPositiveSynergies' => $topPositiveSynergies,
            'topNegativeSynergies' => $topNegativeSynergies
        ];
    }

    /**
     * Analiza las sinergias de una carta específica con el resto del mazo
     * Busca y calcula las sinergias de una carta específica dentro del contexto del mazo y una estrategia dada.
     *
     * Analiza las interacciones de la carta proporcionada con las demás cartas del mazo
     * y con las condiciones generales de la estrategia seleccionada. Utiliza `eval` para procesar condiciones dinámicas.
     *
     * @param object $card La instancia de la carta a analizar.
     * @param string $typeStrategy El tipo de estrategia a considerar para el análisis (ej. 'Analisis', 'Control', 'Ciclo').
     *                             Debe ser una clave válida en `$strategiesMap`.
     * @param string $typeSyn El tipo de sinergia a buscar: 'Cards' (solo entre cartas), 'General' (solo con la estrategia),
     *                        o 'All' (ambas). Por defecto es 'All'.
     * @return array Un array asociativo con los resultados de la búsqueda de sinergias:
     *               - 'pointsSynergy': Puntuación total de sinergia acumulada.
     *               - 'maximumPossiblePoints': Máxima puntuación de sinergia posible según las condiciones evaluadas.
     *               - 'msg': Mensajes descriptivos de las sinergias generales encontradas (obsoleto, usar 'reasons').
     *               - 'reasons': Array de arrays, cada uno con 'reason' (descripción) y 'points' para sinergias generales.
     *               - 'SynergyCards': Array asociativo donde las claves son índices de cartas del mazo y los valores
     *                                 son arrays con 'medium' (URL del icono), 'pointsSynergy' (puntos de esa interacción),
     *                                 'msg' (mensajes de esa interacción específica, obsoleto, usar 'reasons') y
     *                                 'reasons' (array de arrays con 'reason' y 'points' para esa interacción).
     * @throws \InvalidArgumentException Si `$typeStrategy` no es una estrategia válida.
     * @throws \Exception Si ocurre un error durante la evaluación de condiciones (`eval`), al decodificar JSON,
     *                    al acceder a datos de cartas o estrategias, o al calcular estadísticas básicas del mazo.
     * @throws \Error Si `eval()` produce un error irrecuperable.
     */
    public function searchSynergiesCard(object $card, string $typeStrategy, string $typeSyn = 'All'): array
    {
        $strategiesMap = [
            'Analisis' => ['General', 'Defensa', 'Precion', 'Adaptavilidad'],
            'Control' => ['General', 'Defensa', 'Precion', 'Adaptavilidad'],
            'Ciclo' => ['General', 'Defensa', 'Adaptavilidad', 'Ciclo', 'Ciclo'],
            'Push' => ['General', 'Defensa', 'Adaptavilidad', 'Push', 'Push'],
            'Split Push' => ['General', 'Defensa', 'Adaptavilidad', 'SplitPush', 'SplitPush'],
            'Agresivo' => ['General', 'Defensa', 'Precion', 'Adaptavilidad', 'Agresivo', 'Agresivo'],
        ];

        if (!isset($strategiesMap[$typeStrategy]))
            throw new \InvalidArgumentException("Invalid strategy: {$typeStrategy}.");

        $statSyn = [
            'pointsSynergy' => 0.0,
            'maximumPossiblePoints' => 0,
            'msg' => [], // General messages/logs
            'reasons' => [], // Reasons for general synergies
            'SynergyCards' => [] // Details about card-specific synergies
        ];

        $allDeckCards = array_merge($this->deck->Cards, $this->deck->CardsEvo);
        $DeckStatsArray = json_decode(json_encode(value: $allDeckCards), true);
        $staMaz = static::deckStatsBasic($allDeckCards);
        $structureInMazo = in_array('Building', array_column($DeckStatsArray, 'type'));
        $DefWinInMazo = array_filter($allDeckCards, function ($card) {
            return $this->getGroupCard($card) == 'dew';
        });
        $arrspeedCom = ['null', 'Slow', 'Medium', 'Fast', 'Very Fast', 'null'];

        // Pre-calculate special keys for the card being analyzed
        $cardSpecialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
        $cardSpecialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
        $cardChampeonAbilityKeys = ($card->rarity == 'Champion' && isset($card->special->champeonAbility)) ? array_keys(json_decode(json_encode($card->special->champeonAbility), true)) : [];


        $statCard = [
            'groupCard' => $this->getGroupCard($card),
            'specialKeys' => $cardSpecialKeys,
            'specialEvoKeys' => $cardSpecialEvoKeys,
            'champeonAbilityKeys' => $cardChampeonAbilityKeys,
            'diehech' => $this->calculateDiehech($card->hitpoints)
        ];

        foreach ($strategiesMap[$typeStrategy] as $strategyMapName) { // iterate over each strategy in the map
            if ($typeSyn == 'Cards' || $typeSyn == 'All') {
                foreach ($allDeckCards as $cardDeckKey => $cardDeck) { // iterate over each card in the deck
                    if ($cardDeck->name == $card->name)
                        continue;

                    // Initialize the entry for this specific card interaction if it doesn't exist
                    if (!isset($statSyn['SynergyCards'][$cardDeckKey])) {
                        $statSyn['SynergyCards'][$cardDeckKey] = [
                            'medium' => $cardDeck->urlIcon,
                            'pointsSynergy' => 0.0,
                            'msg' => [], // Kept for potential backward compatibility or debugging
                            'reasons' => [] // Initialize reasons for this specific card interaction
                        ];
                    }

                    // Pre-calculate special keys for the deck card
                    $deckCardSpecialKeys = $cardDeck->special ? array_keys(json_decode(json_encode($cardDeck->special), true)) : [];
                    $deckCardSpecialEvoKeys = ($cardDeck->evolution && $cardDeck->specialEvo) ? array_keys(json_decode(json_encode($cardDeck->specialEvo), true)) : [];
                    $deckCardChampeonAbilityKeys = ($cardDeck->rarity == 'Champion' && isset($cardDeck->special->champeonAbility)) ? array_keys(json_decode(json_encode($cardDeck->special->champeonAbility), true)) : [];


                    $statCardDeck = [
                        'groupCard' => $this->getGroupCard($cardDeck),
                        'specialKeys' => $deckCardSpecialKeys,
                        'specialEvoKeys' => $deckCardSpecialEvoKeys,
                        'champeonAbilityKeys' => $deckCardChampeonAbilityKeys,
                        'diehech' => $this->calculateDiehech($cardDeck->hitpoints),
                        'speedSyn' => array_slice($arrspeedCom, (array_search($cardDeck->speed ?? 'Medium', $arrspeedCom) - 1), 3) // Added default speed
                    ];

                    foreach ($this->strategies[$strategyMapName]->Cards as $Condition) { // iterate over each condition in the strategy
                        try {
                            // Suppress errors during eval and check the result later
                            $conditionResult = @eval ("return " . $Condition->condition . ";");
                            if ($conditionResult === false && error_get_last() !== null) {
                                $error = error_get_last();
                                // Log the specific error from eval
                                error_log("Error evaluating condition for card synergy: {$Condition->condition} - Error: {$error['message']} in {$error['file']} on line {$error['line']}");
                                error_clear_last(); // Clear the error after logging
                                continue; // Skip this condition
                            }
                        } catch (\ParseError $e) {
                            // Log or handle the eval parse error specifically
                            error_log("ParseError evaluating condition for card synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                            continue; // Skip this condition
                        } catch (\Throwable $e) {
                            // Catch other potential errors during eval
                            error_log("Error evaluating condition for card synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                            continue; // Skip this condition
                        }


                        if ($conditionResult) {
                            // Accumulate points for the overall card synergy and specific interaction
                            $pointsToAdd = $Condition->points ?? 0; // Default to 0 if points missing
                            $statSyn['pointsSynergy'] += $pointsToAdd;
                            $statSyn['SynergyCards'][$cardDeckKey]['pointsSynergy'] += $pointsToAdd;

                            // Store the descriptive message (optional, can be kept or removed)
                            $statSyn['SynergyCards'][$cardDeckKey]['msg'][] = $strategyMapName . ': ' . ($pointsToAdd >= 0 ? '+' : '') . $pointsToAdd . 'pts ' . $Condition->name;

                            // Store the reason and points for this specific synergy
                            $statSyn['SynergyCards'][$cardDeckKey]['reasons'][] = ['reason' => $Condition->name, 'points' => $pointsToAdd];
                        }
                        // Accumulate maximum possible points regardless of condition result
                        // Ensure points is numeric before using abs()
                        if (isset($Condition->points) && is_numeric($Condition->points)) {
                            $statSyn['maximumPossiblePoints'] += abs($Condition->points); // Use abs() to sum potential positive impact
                        } else {
                            // Log or handle non-numeric/missing points if necessary
                            error_log("Non-numeric or missing points found for condition '{$Condition->name}' in strategy '{$strategyMapName}'.");
                        }
                    }
                }
            }

            if ($typeSyn == 'General' || $typeSyn == 'All') {
                foreach ($this->strategies[$strategyMapName]->General as $Condition) {
                    try {
                        // Suppress errors during eval and check the result later
                        $conditionResult = @eval ("return " . $Condition->condition . ";");
                        if ($conditionResult === false && error_get_last() !== null) {
                            $error = error_get_last();
                            // Log the specific error from eval
                            error_log("Error evaluating condition for general synergy: {$Condition->condition} - Error: {$error['message']} in {$error['file']} on line {$error['line']}");
                            error_clear_last(); // Clear the error after logging
                            continue; // Skip this condition
                        }
                    } catch (\ParseError $e) {
                        // Log or handle the eval parse error specifically
                        error_log("ParseError evaluating condition for general synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                        continue; // Skip this condition
                    } catch (\Throwable $e) {
                        // Catch other potential errors during eval
                        error_log("Error evaluating condition for general synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                        continue; // Skip this condition
                    }


                    if ($conditionResult) {
                        // Accumulate points for the overall synergy
                        $pointsToAdd = $Condition->points ?? 0; // Default to 0 if points missing
                        $statSyn['pointsSynergy'] += $pointsToAdd;

                        // Store the descriptive message (optional, can be kept or removed)
                        $statSyn['msg'][] = $strategyMapName . ': ' . ($pointsToAdd >= 0 ? '+' : '') . $pointsToAdd . 'pts ' . $Condition->name;

                        // Store the reason and points for this general synergy
                        $statSyn['reasons'][] = ['reason' => $Condition->name, 'points' => $pointsToAdd];
                    }
                    // Accumulate maximum possible points regardless of condition result
                    // Ensure points is numeric before using abs()
                    if (isset($Condition->points) && is_numeric($Condition->points)) {
                        $statSyn['maximumPossiblePoints'] += abs($Condition->points); // Use abs() to sum potential positive impact
                    } else {
                        // Log or handle non-numeric/missing points if necessary
                        error_log("Non-numeric or missing points found for general condition '{$Condition->name}' in strategy '{$strategyMapName}'.");
                    }
                }

            }
        }

        return $statSyn;
    }

    /**
     * Determina el grupo principal de una carta (win, ter, aer, dew, hech).
     * Método auxiliar necesario para el análisis de sinergias.
     */
    private function getGroupCard(object $card): string
    {
        // Ensure necessary properties exist and have default values
        $dps = $card->dps ?? 0;
        $hitpoints = $card->hitpoints ?? 0;
        $type = $card->type ?? 'Unknown';
        $attackTargets = $card->Attack ?? ['null', 'null']; // Default to array with 'null'

        // Ensure constants are loaded
        $minDpsDew = $this->constants->MIN_DPS_DEFENSE_WIN_CONDITION ?? 200;
        $minHpDew = $this->constants->MIN_HITPOINTS_DEFENSE_WIN_CONDITION ?? 1000;
        $minDpsBuildingDew = $this->constants->MIN_DPS_DEFENSE_BUILDING ?? 200;

        // Determine group
        if ($type == 'Spell') {
            return 'hech';
        }

        // Check for Defensive Win Condition (dew) - High stats troop/building
        if (
            $type != 'Tower' && // Exclude King/Princess Towers
            (
                ($type == 'Building' && $dps > $minDpsBuildingDew) ||
                ($type != 'Building' && $dps > $minDpsDew && $hitpoints > $minHpDew && (in_array('ter', $attackTargets) || in_array('aer', $attackTargets)))
            )
        ) {
            return 'dew';
        }

        // Check for Win Condition (win) - Primarily targets structures
        if (in_array('est', $attackTargets)) {
            return 'win';
        }

        // Check for Terrestrial (ter) - Targets ground, not a Tower
        if (in_array('ter', $attackTargets) && $type != 'Tower') {
            return 'ter';
        }

        // Check for Aerial (aer) - Targets air OR is a Tower card (Princess Tower)
        if (in_array('aer', $attackTargets) || $type == 'Tower') {
            return 'aer';
        }

        // Fallback or error if no group is matched
        error_log("Card '{$card->name}' (Type: {$type}, Targets: " . implode(',', $attackTargets) . ") could not be grouped.");
        return 'ter'; // Default to 'ter' as a fallback
    }

    /**
     * Calcula el nivel de diehech de una carta
     * Método auxiliar necesario para el análisis de sinergias.
     */
    private function calculateDiehech(int $hitpoints): string|false
    {
        // Ensure constants are loaded and numeric
        $lvl4 = $this->constants->DIEHECH_LEVEL_4 ?? 0;
        $lvl3 = $this->constants->DIEHECH_LEVEL_3 ?? 0;
        $lvl2 = $this->constants->DIEHECH_LEVEL_2 ?? 0;
        $lvl1 = $this->constants->DIEHECH_LEVEL_1 ?? 0;

        if ($hitpoints < $lvl4) {
            return 'hech4';
        } elseif ($hitpoints < $lvl3) {
            return 'hech3';
        } elseif ($hitpoints < $lvl2) {
            return 'hech2';
        } elseif ($hitpoints <= $lvl1) {
            return 'hech1';
        }
        return false;
    }

    /**
     * Calcula estadísticas generales del mazo.
     *
     * Este método procesa un array de cartas que representan un mazo y calcula diversas
     * estadísticas agregadas sobre el mismo.
     *
     * @param array $Mazo0 Un array que representa el mazo de cartas.
     *
     * @return array Un array asociativo con las siguientes estadísticas del mazo:
     *   - elixirCostAll: (int) Costo total de elixir de todas las cartas en el mazo.
     *   - costElixTrops: (int) Costo total de elixir de las cartas que no son hechizos.
     *   - agresividad: (int|float) Suma total del DPS de las cartas que no son hechizos más el daño de los hechizos.
     *   - cardsSplash: (int) Cantidad de cartas con ataque de área (splash) que no son estructuras ('Building').
     *   - cardsUnique: (int) Cantidad de cartas que no tienen ataque de área y no son estructuras.
     *   - unitsAll: (int) Suma total de unidades de todas las cartas en el mazo.
     *   - synMaxAll: (int) Suma total de la sinergia máxima de todas las cartas en el mazo (si está presente).
     *   - spells: (array) Un array con las cartas de tipo 'Spell' en el mazo.
     * @throws \Exception Si ocurre un error al procesar los datos de las cartas (ej. decodificación JSON).
     */
    public static function deckStatsBasic(array $Mazo0): array
    {
        $Mazo = array_filter($Mazo0, fn($a) => $a); // Remove null/false entries
        // No need to re-encode/decode if $Mazo0 already contains objects or associative arrays
        // $Mazo = json_decode(json_encode($Mazo), true);

        foreach ($Mazo as $card) {
            if (!is_object($card) && !is_array($card) && !($card instanceof \stdClass)) {
                throw new \Exception("las cartas del Mazo en 'deckStatsBasic()' deben ser instancias de Card, " . gettype($card) . " encontrado");
            }
        }

        $stats = [
            'elixirCostAll' => 0,
            'costElixTrops' => 0,
            'agresividad' => 0,
            'cardsSplash' => 0,
            'cardsUnique' => 0,
            'unitsAll' => 0,
            'synMaxAll' => 0,
            'spells' => []
        ];

        foreach ($Mazo as $card) {
            $stats['elixirCostAll'] += $card->elixirCost ?? 0;
            $stats['unitsAll'] += $card->units ?? 1; // Default to 1 unit if not specified
            $stats['synMaxAll'] += $card->cardSynMax->synMax ?? 0; // Use null coalescing

            if (($card->type ?? '') == 'Spell') {
                $stats['spells'][] = $card;
                $stats['agresividad'] += $card->damage ?? 0; // Add spell damage to aggressiveness
            } else {
                // For non-spells
                if (($card->type ?? '') != 'Tower') {
                    $stats['costElixTrops'] += $card->elixirCost ?? 0;
                    $stats['agresividad'] += $card->dps ?? 0; // Add troop/building DPS to aggressiveness

                    if (($card->TypeAttack ?? 'unique') == 'splash' && ($card->type ?? '') != 'Building') {
                        $stats['cardsSplash']++;
                    } else {
                        $stats['cardsUnique']++;
                    }
                }
            }
        }


        return $stats;
    }
}
