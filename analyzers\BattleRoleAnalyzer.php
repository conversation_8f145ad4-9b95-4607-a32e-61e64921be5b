<?php

declare(strict_types=1);

namespace tools\deckanalyzer\analyzers;

use tools\deckanalyzer\AnalyzerInterface;

/**
 * Analizador de Roles de Batalla - Evalúa los roles de ataque y defensa del mazo.
 *
 * Este analizador se encarga de:
 * - Calcular promedios de ataque y defensa
 * - Evaluar roles de batalla por grupos de cartas
 * - Determinar porcentajes de contribución por tipo de carta
 * - Analizar estadísticas específicas (DPS, HP, rango)
 */
class BattleRoleAnalyzer implements AnalyzerInterface
{
    /** @var object El mazo que se está analizando */
    private object $deck;

    /** @var object Constantes utilizadas para los cálculos */
    private object $constants;

    /** @var array Todas las cartas con estadísticas de nivel 11 */
    private array $allCardsInstanceLevel11;

    /** @var string Tipo de análisis: 'attack' o 'defense' */
    private string $type;

    public function __construct(object $constants, array $allCardsInstanceLevel11, string $type)
    {
        $this->constants = $constants;
        $this->allCardsInstanceLevel11 = $allCardsInstanceLevel11;
        $this->type = $type;
    }

    public function analyze(object $deck): array
    {
        $this->deck = $deck;
        return $this->getBattleRolesScores($this->type);
    }

    /**
     * Calcula los promedios de ataque o defensa del mazo, considerando estadísticas como DPS, daño y puntos de vida.
     *
     * Este método itera a través de las cartas del mazo, diferenciando entre cartas de ataque y defensa,
     * y aplicando cálculos específicos para cada tipo. También considera el daño de los hechizos de forma separada.
     * Los cálculos tienen en cuenta si la carta tiene evolución y el nivel de las estadísticas relevantes.
     *
     * @param string $type El tipo de promedio a calcular: 'attack' para ataque, 'defense' para defensa.
     * @return array Un array asociativo que contiene:
     *               - 'totalScore': Promedio total de ataque o defensa, ponderando diferentes estadísticas.
     *               - 'scoreDps': Puntuación de DPS normalizada.
     *               - 'scoreHitpoints': Puntuación de puntos de vida normalizada.
     *               - 'scoreRange': Puntuación de rango normalizada.
     *               - 'msg':  Mensajes informativos o de advertencia.
     * @throws \InvalidArgumentException Si el `$type` proporcionado no es 'attack' o 'defense'.
     * @throws \Exception Si ocurre un error al calcular los promedios o roles de batalla.
     */
    public function getBattleRolesScores(string $type): array
    {
        if (!in_array($type, ['attack', 'defense'])) {
            throw new \InvalidArgumentException("Invalid type: {$type}. Allowed types are: attack, defense");
        }

        $stats = [
            'totalScore' => 0,
            'scoreDps' => 0,
            'scoreHitpoints' => 0,
            'scoreRange' => 0,
            'msg' => []
        ];

        // Use only non-tower cards for general role scoring
        $deckCardsForRoles = array_filter(array_merge($this->deck->Cards, $this->deck->CardsEvo), fn($card) => $card->type !== 'Tower');

        $averages = $this->getAverages($deckCardsForRoles, $type);
        // Use all non-tower cards as reference
        $allCardsForReference = array_filter($this->allCardsInstanceLevel11, fn($card) => $card->type !== 'Tower');
        $referenceStats = $this->getAverages($allCardsForReference); // Use 'all' roles for reference
        $ponderation = 1.5; // Ponderación para ajustar la dificultad de alcanzar un buen puntaje

        // Calcular puntajes individuales normalizados (0-100+)
        $stats['scoreDps'] = $averages['dps'] > 0 && $referenceStats['dps'] > 0
            ? round(($averages['dps'] / ($referenceStats['dps'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;
        $stats['scoreHitpoints'] = $averages['hitpoints'] > 0 && $referenceStats['hitpoints'] > 0
            ? round(($averages['hitpoints'] / ($referenceStats['hitpoints'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;
        $stats['scoreRange'] = $averages['range'] > 0 && $referenceStats['range'] > 0
            ? round(($averages['range'] / ($referenceStats['range'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;

        // Calcular puntaje total como promedio de los puntajes individuales
        $scores = [
            $stats['scoreHitpoints'],
            $stats['scoreDps'],
            $stats['scoreRange']
        ];
        // Filtrar scores de 0 para no afectar el promedio si una estadística no aplica (ej. rango en melee)
        $validScores = array_filter($scores, fn($score) => $score > 0);
        $stats['totalScore'] = !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;

        // Advertencias generales basadas en el score total
        if ($stats['totalScore'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El mazo tiene un bajo nivel general de "' . $type . '" (' . $stats['totalScore'] . '). Considera agregar más cartas especializadas en esta área.</span>';
        }

        // Advertencias específicas basadas en puntajes individuales
        if ($stats['scoreDps'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El DPS promedio de ' . $type . ' del mazo es bajo (' . $stats['scoreDps'] . ').</span>';
        }
        if ($stats['scoreHitpoints'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: Los puntos de vida promedio de ' . $type . ' del mazo son bajos (' . $stats['scoreHitpoints'] . ').</span>';
        }
        if ($stats['scoreRange'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El rango promedio de ' . $type . ' del mazo es bajo (' . $stats['scoreRange'] . ').</span>';
        }

        return $stats;
    }

    /**
     * Calcula los promedios de ataque y defensa por grupos de cartas
     * Calcula los porcentajes de contribución al ataque y defensa por grupos de cartas (win condition, terrestre, aéreo, defensivo/win, hechizo).
     *
     * Utiliza los grupos de cartas obtenidos del análisis de versatilidad y calcula qué porcentaje
     * del puntaje total de ataque y defensa aporta cada grupo.
     *
     * @param array $versatilityGroups Array asociativo con las cartas agrupadas por tipo (resultado de `getScoreVersatility`).
     *                                 Ej: ['cardwin' => [...], 'cardter' => [...], ... 'cardhech' => ['hech1' => [...], ...]]
     * @return array Un array asociativo donde las claves son los tipos de grupo ('cardwin', 'cardter', etc.)
     *               y los valores son arrays con los porcentajes de 'ataque' y 'defensa' como strings (ej. '25%').
     * @throws \Exception Si ocurre un error al calcular los promedios de los grupos o al acceder a constantes.
     */
    public function getBattleRolesScoresByGroup(array $versatilityGroups): array
    {
        $numCardsDamageSpell = $totalDamageSpell = 0;
        $allSpells = [];
        foreach ($versatilityGroups['cardhech'] as $spellGroup) {
            $numCardsDamageSpell += count($spellGroup);
            foreach ($spellGroup as $spell) {
                $totalDamageSpell += $spell->damage ?? 0; // Use null coalescing for safety
                $allSpells[] = $spell; // Collect all spell objects
            }
        }

        // Use getAverages for spells to consider their roles
        $spellAveragesAttack = $this->getAverages($allSpells, 'attack');
        $spellAveragesDefense = $this->getAverages($allSpells, 'defense');

        // Use reference stats for normalization (consider using spell-specific reference if available)
        $allCardsForReference = array_filter($this->allCardsInstanceLevel11, fn($card) => $card->type !== 'Tower');
        $referenceStats = $this->getAverages($allCardsForReference); // Use 'all' roles for reference

        $calculateScore = function (array $averages) use ($referenceStats): float {
            $avgHitpoints = ($referenceStats['hitpoints'] > 0) ? ($averages['hitpoints'] / $referenceStats['hitpoints'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            $avgDps = ($referenceStats['dps'] > 0) ? ($averages['dps'] / $referenceStats['dps'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            $avgRange = ($referenceStats['range'] > 0) ? ($averages['range'] / $referenceStats['range'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            // Damage might be more relevant for spells than DPS/Hitpoints
            $avgDamage = ($referenceStats['damage'] > 0) ? ($averages['damage'] / $referenceStats['damage'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

            // Adjust weighting for spells - prioritize damage, maybe range
            $validScores = array_filter([$avgDamage, $avgRange * 0.5]); // Example: Weight damage higher
            return !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;
            // Original calculation: return ($avgHitpoints + $avgDps + $avgRange) / $this->constants->WEIGHTED_AVERAGE_DIVISOR;
        };

        $spellAttackScore = $calculateScore($spellAveragesAttack);
        $spellDefenseScore = $calculateScore($spellAveragesDefense);


        $averagesByGroup = [
            'cardwin' => ['ataque' => 0, 'defensa' => 0],
            'cardter' => ['ataque' => 0, 'defensa' => 0],
            'cardaer' => ['ataque' => 0, 'defensa' => 0],
            'carddew' => ['ataque' => 0, 'defensa' => 0]
        ];

        $groupTypes = ['cardwin', 'cardter', 'cardaer', 'carddew'];
        $totalAttack = $spellAttackScore; // Start with spell score
        $totalDefense = $spellDefenseScore; // Start with spell score

        foreach ($groupTypes as $groupType) {
            $group = $versatilityGroups[$groupType] ?? []; // Use null coalescing
            $count = count($group);

            if ($count === 0) {
                $averagesByGroup[$groupType] = ['ataque' => 0, 'defensa' => 0];
                continue;
            }

            // Recalculate score using the same logic as getBattleRolesScores
            $calculateGroupScore = function (array $averages) use ($referenceStats): float {
                $avgHitpoints = ($referenceStats['hitpoints'] > 0) ? ($averages['hitpoints'] / $referenceStats['hitpoints'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
                $avgDps = ($referenceStats['dps'] > 0) ? ($averages['dps'] / $referenceStats['dps'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
                $avgRange = ($referenceStats['range'] > 0) ? ($averages['range'] / $referenceStats['range'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

                $validScores = array_filter([$avgHitpoints, $avgDps, $avgRange]);
                return !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;
                // Original: return ($avgHitpoints + $avgDps + $avgRange) / $this->constants->WEIGHTED_AVERAGE_DIVISOR;
            };


            $attackScore = $calculateGroupScore($this->getAverages($group, 'attack'));
            $defenseScore = $calculateGroupScore($this->getAverages($group, 'defense'));

            $averagesByGroup[$groupType] = ['ataque' => $attackScore, 'defensa' => $defenseScore];

            $totalAttack += $attackScore;
            $totalDefense += $defenseScore;
        }

        // Add spell scores to the group array
        $averagesByGroup['cardhech'] = ['ataque' => $spellAttackScore, 'defensa' => $spellDefenseScore];

        // Calculate percentages
        foreach ($averagesByGroup as &$averages) {
            $averages['ataque'] = $totalAttack > 0 ? round(($averages['ataque'] / $totalAttack) * 100) . '%' : '0%';
            $averages['defensa'] = $totalDefense > 0 ? round(($averages['defensa'] / $totalDefense) * 100) . '%' : '0%';
        }
        unset($averages); // Break reference

        return $averagesByGroup;
    }

    /**
     * Calcula los promedios de estadísticas específicas para un conjunto de cartas.
     *
     * Este método itera sobre un array de cartas y calcula promedios para varias estadísticas
     * como DPS, daño, puntos de vida, velocidad de ataque y alcance. Permite filtrar
     * las cartas según su rol en batalla ('attack', 'defense' o 'all').
     *
     * @param array $cards Un array de objetos Card sobre los cuales se calcularán los promedios.
     * @param string $roleBatleCards El rol de las cartas a considerar en el cálculo.
     *                                 Puede ser 'all' (todas), 'attack' (ataque) o 'defense' (defensa).
     *                                 Por defecto es 'all'.
     *
     * @return array Un array asociativo con los promedios calculados para cada estadística
     *               ('dps', 'damage', 'hitpoints', 'hitspeed', 'range').  Los valores son
     *               redondeados a dos decimales.  Si no hay cartas para calcular una
     *               estadística en particular, el promedio para esa estadística será 0.
     * @throws \InvalidArgumentException Si el valor de `$roleBatleCards` no es 'all', 'attack' o 'defense'.
     * @throws \Exception Si ocurre un error al obtener el rol de batalla de una carta (`getCardBatleRole`).
     */
    public function getAverages(array $cards, string $roleBatleCards = 'all'): array
    {
        if (!in_array($roleBatleCards, ['all', 'attack', 'defense'])) {
            throw new \InvalidArgumentException("Invalid roleBatleCards: {$roleBatleCards}. Allowed roleBatleCards are: all, attack, defense");
        }

        $averages = $totals = $counts = array_fill_keys(['dps', 'damage', 'hitpoints', 'hitspeed', 'range'], 0);

        foreach ($cards as $card) {
            // Ensure card is an object with expected properties
            if (!is_object($card))
                continue;

            $cardRoleWeight = 1.0; // Default weight

            if ($roleBatleCards != 'all') {
                $roles = $this->getCardBatleRole($card); // Get percentages
                $roleWeight = ($roles[$roleBatleCards] ?? 0) / 100; // Weight based on the desired role percentage

                if ($roleWeight <= 0.1) { // Skip cards with very low relevance to the role (threshold adjustable)
                    continue;
                }
                $cardRoleWeight = $roleWeight; // Use the percentage as the weight
            }

            // Use null coalescing operator for safety
            $dps = $card->dps ?? 0;
            $damage = $card->damage ?? 0;
            $hitpoints = $card->hitpoints ?? 0;
            $hitspeed = $card->hitspeed ?? 0;
            $range = $card->range ?? ($card->radius ?? 0); // Consider radius if range is missing
            $type = $card->type ?? 'Unknown';
            $units = $card->units ?? 1;
            $suicide = $card->suicide ?? false;

            // Apply weight to totals
            if ($type != 'Spell' || ($type == 'Spell' && $units > 0)) { // Include unit-generating spells
                if (!$suicide) {
                    $totals['dps'] += $dps * $cardRoleWeight;
                    $counts['dps'] += $cardRoleWeight;
                    $totals['hitspeed'] += $hitspeed * $cardRoleWeight;
                    $counts['hitspeed'] += $cardRoleWeight;
                }
                if ($type != 'Tower') {
                    $totals['hitpoints'] += $hitpoints * $cardRoleWeight;
                    $counts['hitpoints'] += $cardRoleWeight;
                }
            }

            // Range applies to troops, buildings, and some spells (radius)
            if (is_numeric($range) && $range > 0) {
                $totals['range'] += $range * $cardRoleWeight;
                $counts['range'] += $cardRoleWeight;
            }

            // Damage applies to all card types that deal damage
            if ($damage > 0) {
                $totals['damage'] += $damage * $cardRoleWeight;
                $counts['damage'] += $cardRoleWeight;
            }
        }

        foreach ($totals as $averageType => $total) {
            if ($counts[$averageType] > 0) {
                $averages[$averageType] = round($total / $counts[$averageType], 2);
            }
        }

        return $averages;
    }

    /**
     * Determina los porcentajes de rol 'attack' y 'defense' de una carta.
     *
     * Evalúa múltiples atributos de la carta (objetivo, velocidad, tipo de ataque, rango, tipo de carta, habilidades especiales)
     * para asignar puntuaciones de ataque y defensa, que luego se normalizan a porcentajes.
     *
     * @param object $card La carta a evaluar.
     * @return array Un array asociativo con los porcentajes 'attack' y 'defense' (ej. ['attack' => 70, 'defense' => 30]).
     */
    private function getCardBatleRole(object $card): array
    {
        $attackScore = 50.0; // Start with a baseline assumption of 50/50
        $defenseScore = 50.0;

        // --- Adjust scores based on attributes ---

        // 1. Primary Target (Attack[0]) - Strong indicator
        $primaryTarget = $card->Attack[0] ?? 'null';
        if ($primaryTarget == 'est') { // Targets buildings
            $attackScore += 25;
            $defenseScore -= 15;
        } elseif ($primaryTarget == 'ter' || $primaryTarget == 'aer') { // Targets troops/air
            $defenseScore += 15;
            $attackScore -= 5;
        }
        // Consider Attack[1] if it exists and differs
        $secondaryTarget = $card->Attack[1] ?? 'null';
        if ($secondaryTarget != 'null' && $secondaryTarget != $primaryTarget) {
            if ($secondaryTarget == 'est') {
                $attackScore += 5;
            } // Slight attack boost if secondary is building
            else {
                $defenseScore += 5;
            } // Slight defense boost if secondary is troop/air
        }

        // 2. Movement Speed
        $speed = $card->speed ?? 'Medium';
        if ($speed == 'Very Fast') {
            $attackScore += 15;
            $defenseScore -= 5;
        } elseif ($speed == 'Fast') {
            $attackScore += 8;
        } elseif ($speed == 'Slow') {
            $defenseScore += 8;
            $attackScore -= 3;
        } elseif ($speed == 'Very Slow') {
            $defenseScore += 12;
            $attackScore -= 5;
        }

        // 3. Attack Speed (Hitspeed)
        $hitspeed = $card->hitspeed ?? 1.5; // Assume average if not present
        if ($hitspeed < 1.0) {
            $defenseScore += 8;
        } // Good vs swarms, interrupts
        elseif ($hitspeed > 2.0) {
            $attackScore += 5;
        } // Often high damage per hit, good for burst

        // 4. Damage Type
        $typeAttack = $card->TypeAttack ?? 'unique';
        if ($typeAttack == 'splash') {
            $defenseScore += 15; // Very important for defense against swarms
            $attackScore += 5;  // Can help clear path during offense
        } else { // unique
            $attackScore += 5; // Often better single target dps for offense
            $defenseScore += 5; // Good vs tanks on defense
        }

        // 5. Range (Consider radius for spells/splash)
        $range = $card->range ?? ($card->radius ?? 0);
        if ($range > 5) {
            $attackScore += 5;
            $defenseScore += 8;
        } // Safety for both roles
        elseif ($range < 2 && $range > 0) {
            $defenseScore += 5;
        } // Melee often better suited for defense due to HP/DPS, or needs tank on offense

        // Continue with more attributes in next chunk...

        // --- Normalization ---
        // Ensure scores are not negative
        $attackScore = max(0, $attackScore);
        $defenseScore = max(0, $defenseScore);

        $totalScore = $attackScore + $defenseScore;

        if ($totalScore == 0) {
            // Avoid division by zero, return default 50/50 if no factors applied
            return ['attack' => 50, 'defense' => 50];
        }

        $attackPercent = round(($attackScore / $totalScore) * 100);
        $defensePercent = 100 - $attackPercent; // Ensure it sums to 100

        return ['attack' => $attackPercent, 'defense' => $defensePercent];
    }
}
