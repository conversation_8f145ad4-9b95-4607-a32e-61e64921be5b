<?php

declare(strict_types=1);

namespace tools\deckanalyzer\analyzers;

use tools\deckanalyzer\AnalyzerInterface;

/**
 * Analizador de Versatilidad - Evalúa la cobertura de diferentes tipos de ataque del mazo.
 *
 * Este analizador se encarga de evaluar la versatilidad del mazo mediante:
 * - Cobertura de diferentes tipos de ataque
 * - Distribución de cartas por categorías (win condition, terrestre, aéreo, defensivo, hechizos)
 * - Análisis de combinaciones de hechizos
 */
class VersatilityAnalyzer implements AnalyzerInterface
{
    /** @var object El mazo que se está analizando */
    private object $deck;

    /** @var object Constantes utilizadas para los cálculos */
    private object $constants;

    public function __construct(object $constants)
    {
        $this->constants = $constants;
    }

    public function analyze(object $deck): array
    {
        $this->deck = $deck;
        return $this->getScoreVersatility();
    }

    /**
     * Analiza la versatilidad del mazo evaluando la cobertura de diferentes tipos de ataque.
     *
     * Este método evalúa la versatilidad del mazo mediante la cobertura de diferentes tipos de ataque,
     * asegurando que el mazo tenga una buena distribución de cartas que cubran diversas estrategias.
     * @return array Retorna un array con los resultados del análisis y la versatilidad:
     *               - 'totalScore': Puntuación total de versatilidad (0-100).
     *               - 'cardwin': Array de objetos Card de condición de victoria.
     *               - 'cardter': Array de objetos Card terrestres.
     *               - 'cardaer': Array de objetos Card aéreos.
     *               - 'carddew': Array de objetos Card defensivos/win condition.
     *               - 'cardhech': Array asociativo de objetos Card de hechizos agrupados por tipo ('hech1', 'hech2', etc.).
     *               - 'msg': Mensajes informativos o de advertencia.
     * @throws \Exception Si ocurre un error al determinar el grupo de una carta.
     */
    public function getScoreVersatility(): array
    {
        $versatilityResult = [
            'totalScore' => 0,
            'cardwin' => [],
            'cardter' => [],
            'cardaer' => [],
            'carddew' => [],
            'cardhech' => [
                'hech0' => [],
                'hech1' => [],
                'hech2' => [],
                'hech3' => [],
                'hech4' => []
            ],
            'msg' => []
        ];

        foreach (array_merge($this->deck->Cards, $this->deck->CardsEvo) as $card) {
            $groupCard = $this->getGroupCard($card);
            if ($groupCard != 'hech') {
                $versatilityResult["card{$groupCard}"][] = $card;
            } elseif (isset($card->Attack[0]) && isset($versatilityResult["cardhech"][$card->Attack[0]])) {
                // Check if Attack[0] exists and is a valid key for hechizos
                $versatilityResult["cardhech"][$card->Attack[0]][] = $card;
            } else {
                // Handle cases where spell type is unexpected or missing
                // Maybe log a warning or assign to a default category like 'hech0'
                $versatilityResult["cardhech"]['hech0'][] = $card;
                // Optionally add a message:
                // $versatilityResult['msg'][] = '<span class="cs-color-GoldenYellow text-center">INFO: Spell "' . $card->name . '" has an unrecognized type (' . ($card->Attack[0] ?? 'N/A') . ') and was categorized as generic.</span>';
            }
        }

        $totalVersatility = 0;
        foreach (['cardwin', 'cardter', 'cardaer', 'carddew'] as $type) {
            if (!empty($versatilityResult[$type])) {
                $totalVersatility++;
            } else {
                $versatilityResult['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTINCIA: No hay cartas de tipo ' . $type . ' en el Mazo. Esto puede afectar la versatilidad.</span>';
            }
        }

        $spellsInDeck = [];
        foreach ($versatilityResult['cardhech'] as $type => $spells) {
            foreach ($spells as $spell) {
                // Ensure Attack[0] exists before adding
                if (isset($spell->Attack[0])) {
                    $spellsInDeck[] = $spell->Attack[0];
                }
            }
        }
        $spellsInDeck = array_unique($spellsInDeck); // Avoid duplicates if a spell fits multiple internal types

        $spellVersatility = false;
        foreach ($this->constants->ARRAYS_COMBINATION_SPELL as $type => $combination) {
            // Check if the deck contains *all* spells required by the combination
            if (!empty($combination) && empty(array_diff($combination, $spellsInDeck))) {
                $spellVersatility = true;
                break; // Found a valid combination
            }
        }

        if ($spellVersatility) {
            $totalVersatility++;
        } else {
            $versatilityResult['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTINCIA: La combinación de hechizos no es versátil.</span>';
            $versatilityResult['msg'][] = '<span class="cs-color-GoldenYellow text-center">INFO: Revisa que no haya hechizos con características similares (daño, elixir, etc.).</span>';
        }

        // Avoid division by zero if NUM_CARD_TYPES_VERSATILITY is 0
        $divisor = $this->constants->NUM_CARD_TYPES_VERSATILITY * $this->constants->DIVISOR_VERSATILITY;
        $versatilityResult['totalScore'] = ($divisor > 0)
            ? round((($totalVersatility / $this->constants->NUM_CARD_TYPES_VERSATILITY) / $this->constants->DIVISOR_VERSATILITY) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;

        return $versatilityResult;
    }

    /**
     * Determina el grupo principal de una carta (win, ter, aer, dew, hech).
     *
     * Clasifica la carta basándose en su tipo, objetivo principal y estadísticas (DPS, HP).
     *
     * @param object $card La carta a clasificar.
     * @return string El grupo al que pertenece la carta ('win', 'ter', 'aer', 'dew', 'hech').
     * @throws \Exception Si el tipo de carta no se reconoce o falta información esencial.
     */
    private function getGroupCard(object $card): string
    {
        // Ensure necessary properties exist and have default values
        $dps = $card->dps ?? 0;
        $hitpoints = $card->hitpoints ?? 0;
        $type = $card->type ?? 'Unknown';
        $attackTargets = $card->Attack ?? ['null', 'null']; // Default to array with 'null'

        // Ensure constants are loaded
        $minDpsDew = $this->constants->MIN_DPS_DEFENSE_WIN_CONDITION ?? 200;
        $minHpDew = $this->constants->MIN_HITPOINTS_DEFENSE_WIN_CONDITION ?? 1000;
        $minDpsBuildingDew = $this->constants->MIN_DPS_DEFENSE_BUILDING ?? 200;

        // Determine group
        if ($type == 'Spell') {
            return 'hech';
        }

        // Check for Defensive Win Condition (dew) - High stats troop/building
        if (
            $type != 'Tower' && // Exclude King/Princess Towers
            (
                ($type == 'Building' && $dps > $minDpsBuildingDew) ||
                ($type != 'Building' && $dps > $minDpsDew && $hitpoints > $minHpDew && (in_array('ter', $attackTargets) || in_array('aer', $attackTargets)))
            )
        ) {
            return 'dew';
        }

        // Check for Win Condition (win) - Primarily targets structures
        if (in_array('est', $attackTargets)) {
            return 'win';
        }

        // Check for Terrestrial (ter) - Targets ground, not a Tower
        if (in_array('ter', $attackTargets) && $type != 'Tower') {
            return 'ter';
        }

        // Check for Aerial (aer) - Targets air OR is a Tower card (Princess Tower)
        if (in_array('aer', $attackTargets) || $type == 'Tower') {
            return 'aer';
        }

        // Fallback or error if no group is matched
        // This might indicate an issue with card data or logic
        // Log warning instead of throwing exception to allow analysis to continue
        error_log("Card '{$card->name}' (Type: {$type}, Targets: " . implode(',', $attackTargets) . ") could not be grouped.");
        return 'ter'; // Default to 'ter' as a fallback, or consider 'unknown'
        // throw new \Exception('Tipo de carta "' . $card->name . '" no reconocido o datos insuficientes para agrupar.');
    }
}
